// Vercel Serverless API for MemesByKayé
// Handles CRUD operations on GitHub-stored JSON data

import { Octokit } from '@octokit/rest';

const octokit = new Octokit({
  auth: process.env.GITHUB_TOKEN,
});

const REPO_OWNER = process.env.GITHUB_OWNER || 'your-username';
const REPO_NAME = process.env.GITHUB_REPO || 'MemesByK';
const DATA_PATH = 'data/collections.json';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// Simple in-memory cache to reduce GitHub API calls
let dataCache = null;
let cacheTimestamp = null;
const CACHE_DURATION = 30000; // 30 seconds

// Global cooldown system (server-side)
let globalCooldownEnd = null;
const COOLDOWN_DURATION = 15000; // 15 seconds in milliseconds

function isGlobalCooldownActive() {
  if (!globalCooldownEnd) return false;
  const now = Date.now();
  if (now >= globalCooldownEnd) {
    globalCooldownEnd = null;
    return false;
  }
  return true;
}

function getGlobalCooldownRemaining() {
  if (!globalCooldownEnd) return 0;
  const now = Date.now();
  const remaining = Math.max(0, globalCooldownEnd - now);
  if (remaining === 0) {
    globalCooldownEnd = null;
  }
  return remaining;
}

function activateGlobalCooldown() {
  globalCooldownEnd = Date.now() + COOLDOWN_DURATION;
}

export default async function handler(req, res) {
  // Set CORS headers for all responses
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return res.status(200).json({});
  }

  try {
    switch (req.method) {
      case 'GET':
        // Check if this is a cooldown status request
        if (req.url?.includes('cooldown-status')) {
          return res.status(200).json({
            isActive: isGlobalCooldownActive(),
            remaining: getGlobalCooldownRemaining()
          });
        }
        return await getCollections(req, res);
      case 'POST':
        return await createCollection(req, res);
      case 'PUT':
        return await updateCollection(req, res);
      case 'DELETE':
        return await deleteCollection(req, res);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('API Error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

// Get all collections with caching
async function getCollections(req, res) {
  try {
    // Check cache first
    const now = Date.now();
    if (dataCache && cacheTimestamp && (now - cacheTimestamp) < CACHE_DURATION) {
      return res.status(200).json(dataCache);
    }

    const { data } = await octokit.rest.repos.getContent({
      owner: REPO_OWNER,
      repo: REPO_NAME,
      path: DATA_PATH,
    });

    const content = Buffer.from(data.content, 'base64').toString();
    const collections = JSON.parse(content);

    // Update cache
    dataCache = collections;
    cacheTimestamp = now;

    res.setHeader('Cache-Control', 's-maxage=60, stale-while-revalidate');
    return res.status(200).json(collections);
  } catch (error) {
    if (error.status === 404) {
      // File doesn't exist, return empty collections
      const emptyData = { series: [] };
      dataCache = emptyData;
      cacheTimestamp = Date.now();
      return res.status(200).json(emptyData);
    }
    throw error;
  }
}

// Create new collection
async function createCollection(req, res) {
  const { collection, adminPassword } = req.body;

  // Check global cooldown first
  if (isGlobalCooldownActive()) {
    const remaining = Math.ceil(getGlobalCooldownRemaining() / 1000);
    return res.status(429).json({
      error: 'System cooldown active',
      message: `Please wait ${remaining} seconds before performing another operation`,
      cooldownRemaining: getGlobalCooldownRemaining()
    });
  }

  // Verify admin password with better error handling
  if (!adminPassword || adminPassword.trim() !== process.env.ADMIN_PASSWORD?.trim()) {
    return res.status(401).json({ error: 'Unauthorized access' });
  }

  try {
    // Get current data
    let currentData = { series: [] };
    try {
      const { data } = await octokit.rest.repos.getContent({
        owner: REPO_OWNER,
        repo: REPO_NAME,
        path: DATA_PATH,
      });
      currentData = JSON.parse(Buffer.from(data.content, 'base64').toString());
    } catch (error) {
      // File doesn't exist, use empty data
    }

    // Add new collection
    const newCollection = {
      id: `collection-${Date.now()}`,
      ...collection,
      createdAt: new Date().toISOString(),
    };

    currentData.series.push(newCollection);

    // Update GitHub file
    await updateGitHubFile(currentData, 'Add new collection: ' + collection.title);

    // Clear cache after update
    dataCache = null;
    cacheTimestamp = null;

    // Activate global cooldown after successful operation
    activateGlobalCooldown();

    return res.status(201).json(newCollection);
  } catch (error) {
    throw error;
  }
}

// Update existing collection
async function updateCollection(req, res) {
  const { collectionId, updates, adminPassword } = req.body;

  // Check global cooldown first
  if (isGlobalCooldownActive()) {
    const remaining = Math.ceil(getGlobalCooldownRemaining() / 1000);
    return res.status(429).json({
      error: 'System cooldown active',
      message: `Please wait ${remaining} seconds before performing another operation`,
      cooldownRemaining: getGlobalCooldownRemaining()
    });
  }

  // Verify admin password with better error handling
  if (!adminPassword || adminPassword.trim() !== process.env.ADMIN_PASSWORD?.trim()) {
    return res.status(401).json({ error: 'Unauthorized access' });
  }

  try {
    // Get current data
    const { data } = await octokit.rest.repos.getContent({
      owner: REPO_OWNER,
      repo: REPO_NAME,
      path: DATA_PATH,
    });

    const currentData = JSON.parse(Buffer.from(data.content, 'base64').toString());
    
    // Find and update collection
    const collectionIndex = currentData.series.findIndex(s => s.id === collectionId);
    if (collectionIndex === -1) {
      return res.status(404).json({ error: 'Collection not found' });
    }

    currentData.series[collectionIndex] = {
      ...currentData.series[collectionIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    // Update GitHub file
    await updateGitHubFile(currentData, 'Update collection: ' + currentData.series[collectionIndex].title);

    // Clear cache after update
    dataCache = null;
    cacheTimestamp = null;

    // Activate global cooldown after successful operation
    activateGlobalCooldown();

    return res.status(200).json(currentData.series[collectionIndex]);
  } catch (error) {
    throw error;
  }
}

// Delete collection
async function deleteCollection(req, res) {
  const { collectionId, adminPassword } = req.body;

  // Check global cooldown first
  if (isGlobalCooldownActive()) {
    const remaining = Math.ceil(getGlobalCooldownRemaining() / 1000);
    return res.status(429).json({
      error: 'System cooldown active',
      message: `Please wait ${remaining} seconds before performing another operation`,
      cooldownRemaining: getGlobalCooldownRemaining()
    });
  }

  // Verify admin password with better error handling
  if (!adminPassword || adminPassword.trim() !== process.env.ADMIN_PASSWORD?.trim()) {
    return res.status(401).json({ error: 'Unauthorized access' });
  }

  try {
    // Get current data
    const { data } = await octokit.rest.repos.getContent({
      owner: REPO_OWNER,
      repo: REPO_NAME,
      path: DATA_PATH,
    });

    const currentData = JSON.parse(Buffer.from(data.content, 'base64').toString());
    
    // Find collection to delete
    const collectionIndex = currentData.series.findIndex(s => s.id === collectionId);
    if (collectionIndex === -1) {
      return res.status(404).json({ error: 'Collection not found' });
    }

    const deletedCollection = currentData.series[collectionIndex];
    currentData.series.splice(collectionIndex, 1);

    // Update GitHub file
    await updateGitHubFile(currentData, 'Delete collection: ' + deletedCollection.title);

    // Clear cache after update
    dataCache = null;
    cacheTimestamp = null;

    // Activate global cooldown after successful operation
    activateGlobalCooldown();

    return res.status(200).json({ message: 'Collection deleted successfully' });
  } catch (error) {
    throw error;
  }
}

// Helper function to update GitHub file
async function updateGitHubFile(data, commitMessage) {
  try {
    // Get current file SHA
    let sha;
    try {
      const { data: fileData } = await octokit.rest.repos.getContent({
        owner: REPO_OWNER,
        repo: REPO_NAME,
        path: DATA_PATH,
      });
      sha = fileData.sha;
    } catch (error) {
      // File doesn't exist, no SHA needed for creation
    }

    // Update or create file
    await octokit.rest.repos.createOrUpdateFileContents({
      owner: REPO_OWNER,
      repo: REPO_NAME,
      path: DATA_PATH,
      message: commitMessage,
      content: Buffer.from(JSON.stringify(data, null, 2)).toString('base64'),
      sha: sha,
    });
  } catch (error) {
    console.error('Error updating GitHub file:', error);
    throw error;
  }
}
