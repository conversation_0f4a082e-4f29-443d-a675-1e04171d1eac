// Discord CDN Upload API for MemesByKayé
// Uploads images to Discord and returns CDN URLs

// Global cooldown system (shared with collections.js)
let globalCooldownEnd = null;
const COOLDOWN_DURATION = 15000; // 15 seconds in milliseconds

function isGlobalCooldownActive() {
  if (!globalCooldownEnd) return false;
  const now = Date.now();
  if (now >= globalCooldownEnd) {
    globalCooldownEnd = null;
    return false;
  }
  return true;
}

function getGlobalCooldownRemaining() {
  if (!globalCooldownEnd) return 0;
  const now = Date.now();
  const remaining = Math.max(0, globalCooldownEnd - now);
  if (remaining === 0) {
    globalCooldownEnd = null;
  }
  return remaining;
}

export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).json({});
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { imageData, fileName, adminPassword, metadata } = req.body;

    // Check global cooldown first
    if (isGlobalCooldownActive()) {
      const remaining = Math.ceil(getGlobalCooldownRemaining() / 1000);
      return res.status(429).json({
        error: 'System cooldown active',
        message: `Please wait ${remaining} seconds before performing another operation`,
        cooldownRemaining: getGlobalCooldownRemaining()
      });
    }

    // Verify admin password with trimming
    if (!adminPassword || adminPassword.trim() !== process.env.ADMIN_PASSWORD?.trim()) {
      return res.status(401).json({ error: 'Unauthorized access' });
    }

    // Validate image data
    if (!imageData || !fileName) {
      return res.status(400).json({ error: 'Missing image data or filename' });
    }

    // Convert base64 to buffer
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');

    // Validate file size (5MB max)
    if (buffer.length > 5 * 1024 * 1024) {
      return res.status(400).json({ error: 'File too large. Maximum size is 5MB.' });
    }

    // Upload to Discord webhook
    const discordUrl = await uploadToDiscord(buffer, fileName, metadata);

    return res.status(200).json({
      success: true,
      url: discordUrl,
      fileName: fileName,
      size: buffer.length
    });

  } catch (error) {
    console.error('Upload error:', error);
    return res.status(500).json({ error: 'Upload failed: ' + error.message });
  }
}

async function uploadToDiscord(buffer, fileName, metadata = {}) {
  const webhookUrl = process.env.DISCORD_WEBHOOK_URL;

  if (!webhookUrl) {
    throw new Error('Discord webhook URL not configured');
  }

  try {
    // Import form-data for Node.js environment
    const FormData = require('form-data');
    const form = new FormData();

    // Add the file
    form.append('file', buffer, {
      filename: fileName,
      contentType: getContentType(fileName)
    });

    // Create rich embed with metadata
    const embed = createDiscordEmbed(fileName, metadata);

    // Add payload with embed
    const payload = {
      content: null,
      embeds: [embed]
    };

    form.append('payload_json', JSON.stringify(payload));

    // Use node-fetch for Node.js environment
    const fetch = require('node-fetch');

    // Upload to Discord
    const response = await fetch(webhookUrl, {
      method: 'POST',
      body: form,
      headers: form.getHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Discord upload failed: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    // Extract CDN URL from Discord response
    if (result.attachments && result.attachments.length > 0) {
      return result.attachments[0].url;
    }

    throw new Error('No attachment URL returned from Discord');

  } catch (error) {
    console.error('Discord upload error:', error);
    throw error;
  }
}

function createDiscordEmbed(fileName, metadata) {
  const now = new Date();
  const fileSize = metadata.fileSize ? `${(metadata.fileSize / 1024).toFixed(1)} KB` : 'Unknown';

  const embed = {
    title: "🎭 New Meme Uploaded!",
    description: metadata.title ? `**${metadata.title}**` : `New meme: ${fileName}`,
    color: 0x4FACFE, // Electric blue color matching the site theme
    fields: [
      {
        name: "📁 File Details",
        value: `**Name:** ${fileName}\n**Size:** ${fileSize}`,
        inline: true
      }
    ],
    footer: {
      text: "MemesByKayé • Serverless Edition",
      icon_url: "https://via.placeholder.com/32x32/4FACFE/FFFFFF?text=K"
    },
    timestamp: now.toISOString()
  };

  // Add collection info if available
  if (metadata.collection) {
    embed.fields.push({
      name: "📚 Collection",
      value: metadata.collection,
      inline: true
    });
  }

  // Add description if available
  if (metadata.description) {
    embed.fields.push({
      name: "📝 Description",
      value: metadata.description,
      inline: false
    });
  }

  // Add tags if available
  if (metadata.tags && metadata.tags.length > 0) {
    embed.fields.push({
      name: "🏷️ Tags",
      value: metadata.tags.join(', '),
      inline: false
    });
  }

  return embed;
}

function getContentType(fileName) {
  const ext = fileName.toLowerCase().split('.').pop();
  const types = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp'
  };
  return types[ext] || 'image/jpeg';
}
